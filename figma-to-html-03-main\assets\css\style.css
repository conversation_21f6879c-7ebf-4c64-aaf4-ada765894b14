* {
  box-sizing: border-box;
}

:root {
  --header-bg: #10375c;
}

html {
  font-size: 62.5%;
}

body {
  font-size: 1.6rem;
  font-family: "Sora", sans-serif;
}

/* ============== common ================= */
input,
textarea,
select,
button {
  font-family: inherit;
  font-size: inherit;
}

.container {
  width: 1170px;
  max-width: calc(100% - 48px);
  margin: 0 auto;
}

a {
  text-decoration: none;
}

.btn {
  display: inline-block;
  min-width: 205px;
  line-height: 60px;
  background: #2e80ce;
  color: #fff;
  text-align: center;
  font-size: 1.8rem;
  font-style: normal;
  font-weight: 400;
  border-radius: 999px;
}

.btn:hover {
  opacity: 0.9;
}

.section-heading {
  color: #10375c;
  font-size: 5rem;
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -1px;
}

.section-desc {
  margin-top: 18px;
  color: #575f66;
  font-size: 1.8rem;
  font-weight: 300;
  line-height: 1.67;
}
/* control btn */
.control {
  display: flex;
  column-gap: 18px;
  align-items: center;
}

.control__btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #575f66;
  background: #fff;
  cursor: pointer;
  transition: 0.25s;
}

.control__btn:hover {
  background: #2e80ce;
  border-color: #2e80ce;
  color: #fff;
}

.control-icon--next {
  rotate: 180deg;
}
/* ================ header ================ */
.header {
  padding-top: 30px;
  background: var(--header-bg);
  min-height: 100vh;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar__list {
  display: flex;
}

.header__action--login,
.navbar__link {
  padding: 8px 21px;
  color: #c9d2da;
  font-size: 1.6rem;
  font-style: normal;
  font-weight: 300;
  line-height: 1.75;
}

.navbar__link:hover,
.navbar__link--active {
  text-shadow: 1px 0 0 currentColor;
  color: #fff;
}

.navbar__link--active::before {
  content: "";
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #fff;
  top: -3px;
  position: relative;
  right: 8px;
}

.header__action--signup {
  min-width: 144px;
  line-height: 44px;
  font-size: 1.6rem;
}

.header__action--login {
  margin-right: 7px;
}

/* =================== hero ========================== */
.hero {
  display: flex;
  padding-top: 76px;
  align-items: center;
}

.hero__content {
  width: 44%;
}

.hero__media {
  flex: 1;
}

.hero__heading {
  color: #fff;
  font-size: 7rem;
  font-style: normal;
  font-weight: 600;
  line-height: 1.14;
  letter-spacing: -1.4px;
}

.hero__desc {
  margin-top: 22px;
  color: #c9d2da;
  font-family: Sora;
  font-size: 1.8rem;
  font-weight: 300;
  line-height: 1.67;
}

.hero__row {
  margin-top: 38px;
  display: flex;
  align-items: center;
  column-gap: 38px;
}

.hero__phone {
  color: #fff;
  text-align: center;
  font-size: 1.8rem;
  line-height: 1.67;
}

.hero__images {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.hero__img {
  border-radius: 6px;
  object-fit: cover;
}

.hero__img-large {
  width: 330px;
  height: 540px;
  margin-right: -6px;
  z-index: 1;
}

.hero__img-small {
  width: 210px;
  height: 410px;
}

/* =================== service ========================== */
.service {
  padding: 170px 0;
  background: #fff;
}

.service__heading,
.service__desc {
  text-align: center;
}

.service__desc {
  width: 468px;
  margin: 18px auto 0;
}

.service__row {
  margin-top: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service__img {
  width: 570px;
  height: 570px;
  border-radius: 12px;
  object-fit: cover;
}

.service-item {
  padding: 21px;
  display: flex;
  width: 470px;
  border-radius: 10px;
  background: #fff;
  transition: 0.25s;
}

.service-item:hover {
  position: relative;
  box-shadow: 0px 14px 28px 0px rgba(0, 0, 0, 0.05);
}

.service-item:hover .service-item__icon {
  color: #fff;
  background: #2e80ce;
}

.service-item__icon {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: #eff6fd;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  margin-right: 19px;
  transition: 0.25s;
}

.service-item__heading {
  color: #10375c;
  font-size: 1.8rem;
  font-weight: 600;
  line-height: normal;
}

.service-item__desc {
  margin-top: 8px;
  color: #575f66;
  font-size: 1.6rem;
  font-weight: 300;
  line-height: 1.75;
}

.service__btn {
  margin-top: 55px;
}

/* =================== work ========================== */
.work {
  background: #f6fbff;
  padding: 100px 0;
}

.work__desc {
  width: 468px;
}

.work__list {
  margin-top: 86px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 30px;
}

.work-item {
  padding: 43px;
  border-radius: 4px;
  background: #fff;
  border: 16px solid #eaf6ff;
  transition: 0.25s;
}

.work-item:hover {
  border-color: #2e80ce;
  transform: translateY(-16px);
}

.work-item__heading {
  margin-top: 18px;
  color: #10375c;
  font-size: 2.6rem;
  font-weight: 600;
  line-height: 1.38;
}

.work-item__desc {
  margin-top: 28px;
  color: #575f66;
  font-size: 1.6rem;
  font-weight: 300;
  line-height: 1.75;
}

.work-item__more {
  margin-top: 18px;
  display: inline-block;
  color: #2e80ce;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 1.75;
}

/* =================== about ========================== */
.about {
  margin-top: 85px;
  padding: 85px 0 206px;
}

.about-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.about__media {
  width: 49%;
}

.about-content {
  width: 40%;
}

.about__img {
  position: relative;
}

.about__img--big {
  display: flex;
  object-fit: cover;
  width: 512px;
  height: 370px;
  border-radius: 6px;
  box-shadow: 0px 14px 28px 0px rgba(0, 0, 0, 0.07);
}

.about__img--small {
  position: absolute;
  top: 121px;
  left: 238px;
  object-fit: cover;
  width: 332px;
  height: 370px;
  border-radius: 6px;
}

.about-content__btn {
  margin-top: 38px;
}

/* =================== team ========================== */
.team {
  padding: 85px 0;
}

.team-header {
  text-align: center;
  margin: 0 auto;
}

.team__cta {
  margin-top: 38px;
}

.team__list {
  margin-top: 70px;
  column-gap: 30px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}

.team-item__thumb {
  height: 290px;
  max-width: 100%;
  object-fit: cover;
  display: block;
  transition: 0.5s;
}

.team-item__img-bg {
  border-radius: 6px;
  background: #eaf6ff;
  padding-top: 28px;
  overflow: hidden;
}

.team-item:hover .team-item__thumb {
  transform: scale(1.1);
}

.team-item__name {
  margin-top: 12px;
  color: #10375c;
  text-align: center;
  font-size: 1.6rem;
  line-height: 1.75;
}

.team-item__desc {
  color: #575f66;
  text-align: center;
  font-size: 1.4rem;
  font-weight: 300;
  line-height: 1.86;
}

.team__control {
  margin-top: 38px;
  justify-content: center;
}

/* ======================== install ===================== */
.install__inner {
  margin: 180px 0 85px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 70px;
  height: 427px;
  border-radius: 4px;
  background: #2e80ce;
}

.install__img {
  position: absolute;
  bottom: 0;
  width: 284px;
  height: 522px;
  object-fit: cover;
}

.install__content {
  min-width: 586px;
}

.install__heading {
  color: #fff;
  max-width: 489px;
}

.install__desc {
  margin-top: 18px;
  max-width: 586px;
  color: #fff;
  font-size: 1.8rem;
  font-weight: 300;
  line-height: 1.67;
}

.install__row {
  margin-top: 38px;
  display: flex;
  column-gap: 28px;
  align-items: center;
}

/* ======================== blog ===================== */
.blog {
  padding: 85px 0;
  background: #fff;
}

.blog__inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.blog__content {
  width: 40%;
}

.blog__heading {
  max-width: 470px;
}

.blog__more {
  margin-top: 28px;
  display: inline-block;
  color: #10375c;
  font-size: 1.8rem;
  font-weight: 600;
  line-height: 1.56;
  text-decoration-line: underline;
  text-underline-offset: 5px;
}

.blog__control {
  margin-top: 28px;
  justify-content: flex-start;
}

.blog__list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 30px;
}
.blog-item {
  max-width: 270px;
  border-radius: 12px;
  border: 1px solid #e8ebee;
  background: #fff;
  overflow: hidden;
  transition: 0.5s;
}

.blog-item:hover {
  box-shadow: 0px 18px 50px 0px rgba(0, 0, 0, 0.05);
  border-color: transparent;
}

.blog-item:hover .blog-item__thumb {
  transform: scale(1.1);
}

.blog-item__thumb {
  width: 270px;
  height: 172px;
  border-radius: 12px 12px 0px 0px;
  object-fit: cover;
  transition: 0.25s;
}

.blog-item__wrap {
  height: 172px;
  overflow: hidden;
}

.blog-item__body {
  padding: 18px 22px;
}

.blog-item__heading {
  color: #10375c;
  font-size: 1.6rem;
  font-weight: 600;
  line-height: 1.62;
}

.blog-item__desc {
  margin-top: 10px;
  color: #575f66;
  font-size: 1.4rem;
  font-weight: 300;
  line-height: 1.72;
}

.blog-item__more {
  display: inline-block;
  margin-top: 12px;
  color: #10375c;
  font-size: 1.4rem;
  font-weight: 400;
}

.blog-item__more:hover {
  color: #2e80ce;
}

/* ==================== Appointment ====================== */
.appointment {
  margin-top: 85px;
}

.appointment__inner {
  padding: 100px 178px;
  background: #f6fbff;
  text-align: center;
}

.appointment__heading::after {
  content: "";
  display: block;
  width: 136px;
  height: 2px;
  background: #2e80ce;
  margin: 18px auto 0;
}

.appointment__desc {
  margin: 18px auto 0;
  max-width: 617px;
}

.appointment__btn {
  margin-top: 38px;
}

/* ==================== footer ====================== */
.footer {
  margin-top: 170px;
  padding-top: 100px;
  background: #10375c;
  font-family: "Open Sans", sans-serif;
}

.footer__top {
  display: grid;
  grid-template-columns: 1fr 0.4fr 0.4fr 1fr;
  column-gap: 78px;
}

.footer__desc {
  margin-top: 21px;
  color: #a9b3bb;
  font-size: 1.6rem;
  line-height: 1.75;
}

.footer__heading {
  color: #fff;
  font-size: 1.6rem;
  font-weight: 600;
  line-height: 1.75;
}

.footer__list {
  margin: 20px 0 28px;
}

.footer__link {
  color: #a9b3bb;
  font-size: 1.4rem;
  line-height: 1.86;
  white-space: nowrap;
}

.footer__link:hover {
  text-decoration: underline;
}

.footer__item {
  margin-bottom: 10px;
}

.footer__social {
  margin: 20px 0 28px;
  display: flex;
  column-gap: 10px;
}

.footer__social-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #fff;
  color: #2e80ce;
  transition: 0.25s;
}

.footer__social-btn:hover {
  background: #2e80ce;
  color: #fff;
}

.footer-form {
  display: flex;
  column-gap: 12px;
  margin-top: 10px;
}

.footer-form__input {
  width: 212px;
  height: 46px;
  border-radius: 4px;
  border: 1px solid #406181;
  background: transparent;
  outline: none;
  color: #fff;
}

.footer-form__input::placeholder {
  color: #9ca8b1;
  font-size: 1.4rem;
  line-height: 1.86;
}

.footer-form__submit {
  width: 108px;
  height: 46px;
  border-radius: 4px;
  background: #2e80ce;
  color: #fff;
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 1.86;
  border: none;
}

.footer-form__submit:hover {
  cursor: pointer;
  opacity: 0.9;
}

.footer__copyright {
  border-top: 1px solid #406181;
}

.footer__copyright-text {
  padding: 28px 0 42px;
  color: #aab3ba;
  text-align: center;
  font-size: 1.4rem;
  line-height: 1.86;
}
